#!/usr/bin/env python3
"""
Example script demonstrating how ESP32 would interact with the meal logs API.

This script simulates the ESP32 Smart Plate device posting meal data to the backend.
"""

import requests
from datetime import datetime, timezone
from pathlib import Path
import json


class ESP32MealLogClient:
    """Client for ESP32 to interact with Smart Plate Backend API."""
    
    def __init__(self, base_url: str, device_id: str, access_token: str):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL of the API (e.g., "http://localhost:8000/api")
            device_id: Device identifier
            access_token: Device session access token
        """
        self.base_url = base_url.rstrip('/')
        self.device_id = device_id
        self.access_token = access_token
        self.session = requests.Session()
        
        # Set default headers for device authentication
        self.session.headers.update({
            'device-id': self.device_id,
            'authorization': f'Bearer {self.access_token}'
        })
    
    def post_meal_log(
        self,
        image_path: str,
        total_weight_g: float,
        delta_weight_g: float,
        timestamp: datetime = None
    ) -> dict:
        """
        Post a meal log to the backend.
        
        Args:
            image_path: Path to the food image file
            total_weight_g: Total weight on the plate in grams
            delta_weight_g: Weight change from previous measurement
            timestamp: Capture timestamp (defaults to now)
            
        Returns:
            API response as dictionary
        """
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        
        # Prepare form data
        data = {
            'timestamp': timestamp.isoformat(),
            'total_weight_g': str(total_weight_g),
            'delta_weight_g': str(delta_weight_g),
        }
        
        # Prepare image file
        image_file = Path(image_path)
        if not image_file.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        with open(image_file, 'rb') as f:
            files = {'image_file': (image_file.name, f, 'image/jpeg')}
            
            response = self.session.post(
                f'{self.base_url}/v1/meal_logs/',
                data=data,
                files=files
            )
        
        if response.status_code == 201:
            return response.json()
        else:
            response.raise_for_status()


def main():
    """Example usage of the ESP32 meal log client."""
    
    # Configuration (these would be stored in ESP32 flash memory)
    API_BASE_URL = "http://localhost:8000/api"
    DEVICE_ID = "ESP32_DEMO_001"
    ACCESS_TOKEN = "your_device_access_token_here"
    
    # Initialize client
    client = ESP32MealLogClient(API_BASE_URL, DEVICE_ID, ACCESS_TOKEN)
    
    # Example 1: First food item (Idli)
    print("📸 Posting first food item (Idli)...")
    try:
        response1 = client.post_meal_log(
            image_path="sample_images/idli.jpg",  # This would be captured by ESP32 camera
            total_weight_g=140.0,  # From load cell
            delta_weight_g=140.0,  # First item, so delta equals total
        )
        
        print("✅ Success!")
        print(f"   Meal Log ID: {response1['meal_log_id']}")
        print(f"   Session ID: {response1['meal_session_id']}")
        print(f"   Status: {response1['status']}")
        print(f"   Detected: {len(response1['entries'])} food item(s)")
        
        if response1['entries']:
            entry = response1['entries'][0]
            print(f"   Food: {entry['label']} - {entry['weight_g']}g - {entry['estimated_kcal']} kcal")
        
        print(f"   Session Total: {response1['session_totals']['total_kcal']} kcal")
        print()
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error posting first meal log: {e}")
        return
    
    # Example 2: Second food item (Sambar) - added to same session
    print("📸 Posting second food item (Sambar)...")
    try:
        response2 = client.post_meal_log(
            image_path="sample_images/idli_sambar.jpg",  # Image now shows both items
            total_weight_g=200.0,  # Total weight increased
            delta_weight_g=60.0,   # Only the sambar weight
        )
        
        print("✅ Success!")
        print(f"   Meal Log ID: {response2['meal_log_id']}")
        print(f"   Session ID: {response2['meal_session_id']}")
        print(f"   Same session: {response1['meal_session_id'] == response2['meal_session_id']}")
        print(f"   Status: {response2['status']}")
        print(f"   Detected: {len(response2['entries'])} food item(s)")
        
        if response2['entries']:
            entry = response2['entries'][0]
            print(f"   New Food: {entry['label']} - {entry['weight_g']}g - {entry['estimated_kcal']} kcal")
        
        print(f"   Session Total: {response2['session_totals']['total_kcal']} kcal")
        print()
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error posting second meal log: {e}")
        return
    
    # Example 3: Ambiguous classification
    print("📸 Posting ambiguous food item...")
    try:
        response3 = client.post_meal_log(
            image_path="sample_images/ambiguous_food.jpg",
            total_weight_g=250.0,
            delta_weight_g=50.0,
        )
        
        print("✅ Success!")
        print(f"   Status: {response3['status']}")
        
        if response3['status'] == 'pending_label':
            print("   ⚠️  Food recognition uncertain")
            if response3['ai_suggestions']:
                print("   AI Suggestions:")
                for suggestion in response3['ai_suggestions']:
                    print(f"     - {suggestion['label']} ({suggestion['confidence']:.1%} confidence)")
            else:
                print("   No AI suggestions available")
        
        print()
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error posting ambiguous meal log: {e}")


def create_sample_response():
    """Create a sample response for documentation purposes."""
    sample_response = {
        "meal_log_id": "123e4567-e89b-12d3-a456-426614174000",
        "meal_session_id": "987fcdeb-51a2-43d1-b789-123456789abc",
        "status": "confirmed",
        "image_url": "https://cdn.smartplate.ai/images/abc123.jpg",
        "ai_suggestions": [
            {
                "label": "Idli",
                "confidence": 0.85
            }
        ],
        "entries": [
            {
                "id": 1,
                "label": "Idli",
                "weight_g": 140.0,
                "estimated_kcal": 81.2,
                "protein_g": 2.8,
                "carbs_g": 16.8,
                "fat_g": 0.42,
                "fibre_g": 1.12,
                "source": "ai_auto",
                "manual_override": False
            }
        ],
        "session_totals": {
            "total_kcal": 81.2,
            "protein_g": 2.8,
            "carbs_g": 16.8,
            "fat_g": 0.42,
            "fibre_g": 1.12
        }
    }
    
    print("📋 Sample API Response:")
    print(json.dumps(sample_response, indent=2))


if __name__ == "__main__":
    print("🍽️  Smart Plate ESP32 API Client Example")
    print("=" * 50)
    print()
    
    # Show sample response format
    create_sample_response()
    print()
    
    # Run examples (commented out since we don't have actual images or running server)
    # main()
    
    print("💡 To run the actual examples:")
    print("   1. Start the Smart Plate Backend server")
    print("   2. Create a device and get an access token")
    print("   3. Place sample images in 'sample_images/' directory")
    print("   4. Uncomment the main() call above")
