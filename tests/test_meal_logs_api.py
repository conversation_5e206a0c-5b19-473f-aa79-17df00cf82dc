"""
Tests for meal logs API endpoints.
"""

import io
import json
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from django.utils import timezone as django_timezone
from fastapi.testclient import TestClient
from PIL import Image

from app.api.main import app
from app.models import Device, DeviceSession, UserDevice, MealSession, MealLog, FoodEntry

User = get_user_model()


class TestMealLogsAPI(TestCase):
    """Test cases for meal logs API."""
    
    def setUp(self):
        """Set up test data."""
        self.client = TestClient(app)
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test device
        self.device = Device.objects.create(
            device_id='ESP32_TEST_001',
            psk_hash='test_hash',
            model='SP-2024A',
            firmware_version='1.0.0',
            is_registered=True
        )
        
        # Create device session
        self.device_session = DeviceSession.objects.create(
            device=self.device,
            access_token='test_access_token_123',
            nonce_used='test_nonce',
            expires_at=django_timezone.now() + django_timezone.timedelta(hours=1)
        )
        
        # Link device to user
        self.user_device = UserDevice.objects.create(
            user=self.user,
            device=self.device,
            is_owner=True,
            setup_complete=True
        )
        
        # Common headers for device authentication
        self.auth_headers = {
            'device-id': self.device.device_id,
            'authorization': f'Bearer {self.device_session.access_token}'
        }
    
    def create_test_image(self) -> bytes:
        """Create a test image file."""
        image = Image.new('RGB', (640, 480), color='red')
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        return img_buffer.getvalue()
    
    def test_create_meal_log_success(self):
        """Test successful meal log creation."""
        image_data = self.create_test_image()
        
        # Mock AI classification service
        with patch('app.services.ai_classification.ai_service.classify_food') as mock_classify:
            mock_result = Mock()
            mock_result.label = 'Idli'
            mock_result.confidence = 0.85
            mock_result.weight_g = Decimal('140')
            mock_result.estimated_kcal = Decimal('81.2')
            mock_result.protein_g = Decimal('2.8')
            mock_result.carbs_g = Decimal('16.8')
            mock_result.fat_g = Decimal('0.42')
            mock_result.fibre_g = Decimal('1.12')
            mock_classify.return_value = [mock_result]
            
            response = self.client.post(
                '/api/v1/meal_logs/',
                headers=self.auth_headers,
                data={
                    'timestamp': '2024-01-15T08:30:00Z',
                    'total_weight_g': '140.0',
                    'delta_weight_g': '140.0',
                },
                files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
            )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        
        # Verify response structure
        self.assertIn('meal_log_id', data)
        self.assertIn('meal_session_id', data)
        self.assertEqual(data['status'], 'confirmed')
        self.assertIn('image_url', data)
        self.assertEqual(len(data['entries']), 1)
        self.assertEqual(data['entries'][0]['label'], 'Idli')
        self.assertEqual(float(data['entries'][0]['weight_g']), 140.0)
        
        # Verify session totals
        self.assertIn('session_totals', data)
        self.assertEqual(float(data['session_totals']['total_kcal']), 81.2)
        
        # Verify database records were created
        self.assertTrue(MealLog.objects.filter(id=data['meal_log_id']).exists())
        self.assertTrue(MealSession.objects.filter(id=data['meal_session_id']).exists())
        self.assertEqual(FoodEntry.objects.count(), 1)
    
    def test_create_meal_log_ambiguous_classification(self):
        """Test meal log creation with ambiguous AI classification."""
        image_data = self.create_test_image()
        
        with patch('app.services.ai_classification.ai_service.classify_food') as mock_classify:
            # Mock ambiguous results
            mock_result1 = Mock()
            mock_result1.label = 'Mini Idli'
            mock_result1.confidence = 0.55
            mock_result1.weight_g = Decimal('60')
            mock_result1.estimated_kcal = Decimal('34.8')
            mock_result1.protein_g = Decimal('1.2')
            mock_result1.carbs_g = Decimal('7.2')
            mock_result1.fat_g = Decimal('0.18')
            mock_result1.fibre_g = Decimal('0.48')
            
            mock_result2 = Mock()
            mock_result2.label = 'Kozhukattai'
            mock_result2.confidence = 0.52
            mock_result2.weight_g = Decimal('60')
            mock_result2.estimated_kcal = Decimal('40.0')
            mock_result2.protein_g = Decimal('1.5')
            mock_result2.carbs_g = Decimal('8.0')
            mock_result2.fat_g = Decimal('0.2')
            mock_result2.fibre_g = Decimal('0.5')
            
            mock_classify.return_value = [mock_result1, mock_result2]
            
            response = self.client.post(
                '/api/v1/meal_logs/',
                headers=self.auth_headers,
                data={
                    'timestamp': '2024-01-15T08:30:00Z',
                    'total_weight_g': '60.0',
                    'delta_weight_g': '60.0',
                },
                files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
            )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        
        # Should be pending_label due to ambiguous results
        self.assertEqual(data['status'], 'pending_label')
        self.assertEqual(len(data['ai_suggestions']), 2)
        self.assertEqual(len(data['entries']), 2)  # Both entries created since confidence > 0.4
    
    def test_create_meal_log_low_confidence(self):
        """Test meal log creation with low confidence AI classification."""
        image_data = self.create_test_image()
        
        with patch('app.services.ai_classification.ai_service.classify_food') as mock_classify:
            # Mock low confidence (empty results)
            mock_classify.return_value = []
            
            response = self.client.post(
                '/api/v1/meal_logs/',
                headers=self.auth_headers,
                data={
                    'timestamp': '2024-01-15T08:30:00Z',
                    'total_weight_g': '50.0',
                    'delta_weight_g': '50.0',
                },
                files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
            )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        
        # Should be pending_label with no entries
        self.assertEqual(data['status'], 'pending_label')
        self.assertEqual(len(data['ai_suggestions']), 0)
        self.assertEqual(len(data['entries']), 0)
    
    def test_create_meal_log_invalid_device(self):
        """Test meal log creation with invalid device ID."""
        image_data = self.create_test_image()
        
        invalid_headers = {
            'device-id': 'INVALID_DEVICE',
            'authorization': f'Bearer {self.device_session.access_token}'
        }
        
        response = self.client.post(
            '/api/v1/meal_logs/',
            headers=invalid_headers,
            data={
                'timestamp': '2024-01-15T08:30:00Z',
                'total_weight_g': '140.0',
                'delta_weight_g': '140.0',
            },
            files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
        )
        
        self.assertEqual(response.status_code, 401)
        self.assertIn('Device not found', response.json()['detail'])
    
    def test_create_meal_log_expired_token(self):
        """Test meal log creation with expired device token."""
        # Create expired session
        expired_session = DeviceSession.objects.create(
            device=self.device,
            access_token='expired_token',
            nonce_used='expired_nonce',
            expires_at=django_timezone.now() - django_timezone.timedelta(hours=1)
        )
        
        image_data = self.create_test_image()
        expired_headers = {
            'device-id': self.device.device_id,
            'authorization': f'Bearer {expired_session.access_token}'
        }
        
        response = self.client.post(
            '/api/v1/meal_logs/',
            headers=expired_headers,
            data={
                'timestamp': '2024-01-15T08:30:00Z',
                'total_weight_g': '140.0',
                'delta_weight_g': '140.0',
            },
            files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
        )
        
        self.assertEqual(response.status_code, 401)
        self.assertIn('Invalid or expired', response.json()['detail'])
    
    def test_create_meal_log_device_not_linked_to_user(self):
        """Test meal log creation with device not linked to any user."""
        # Remove user device link
        self.user_device.delete()
        
        image_data = self.create_test_image()
        
        response = self.client.post(
            '/api/v1/meal_logs/',
            headers=self.auth_headers,
            data={
                'timestamp': '2024-01-15T08:30:00Z',
                'total_weight_g': '140.0',
                'delta_weight_g': '140.0',
            },
            files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
        )
        
        self.assertEqual(response.status_code, 403)
        self.assertIn('not linked to any user', response.json()['detail'])
    
    def test_create_meal_log_invalid_image_format(self):
        """Test meal log creation with invalid image format."""
        # Create a text file instead of image
        text_data = b'This is not an image'
        
        response = self.client.post(
            '/api/v1/meal_logs/',
            headers=self.auth_headers,
            data={
                'timestamp': '2024-01-15T08:30:00Z',
                'total_weight_g': '140.0',
                'delta_weight_g': '140.0',
            },
            files={'image_file': ('test.txt', text_data, 'text/plain')}
        )
        
        self.assertEqual(response.status_code, 400)
        self.assertIn('Invalid image file', response.json()['detail'])
    
    def test_create_meal_log_invalid_weight_values(self):
        """Test meal log creation with invalid weight values."""
        image_data = self.create_test_image()
        
        response = self.client.post(
            '/api/v1/meal_logs/',
            headers=self.auth_headers,
            data={
                'timestamp': '2024-01-15T08:30:00Z',
                'total_weight_g': '50.0',
                'delta_weight_g': '-100.0',  # Invalid: delta exceeds total
            },
            files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
        )
        
        self.assertEqual(response.status_code, 400)
        self.assertIn('Invalid weight values', response.json()['detail'])
    
    def test_create_meal_log_missing_headers(self):
        """Test meal log creation with missing authentication headers."""
        image_data = self.create_test_image()
        
        response = self.client.post(
            '/api/v1/meal_logs/',
            data={
                'timestamp': '2024-01-15T08:30:00Z',
                'total_weight_g': '140.0',
                'delta_weight_g': '140.0',
            },
            files={'image_file': ('test.jpg', image_data, 'image/jpeg')}
        )
        
        self.assertEqual(response.status_code, 422)  # FastAPI validation error
    
    def test_meal_session_grouping(self):
        """Test that meal logs are grouped into sessions correctly."""
        image_data = self.create_test_image()
        
        with patch('app.services.ai_classification.ai_service.classify_food') as mock_classify:
            mock_result = Mock()
            mock_result.label = 'Rice'
            mock_result.confidence = 0.8
            mock_result.weight_g = Decimal('100')
            mock_result.estimated_kcal = Decimal('130')
            mock_result.protein_g = Decimal('2.7')
            mock_result.carbs_g = Decimal('28')
            mock_result.fat_g = Decimal('0.3')
            mock_result.fibre_g = Decimal('0.4')
            mock_classify.return_value = [mock_result]
            
            # Create first meal log
            response1 = self.client.post(
                '/api/v1/meal_logs/',
                headers=self.auth_headers,
                data={
                    'timestamp': '2024-01-15T12:00:00Z',
                    'total_weight_g': '100.0',
                    'delta_weight_g': '100.0',
                },
                files={'image_file': ('test1.jpg', image_data, 'image/jpeg')}
            )
            
            # Create second meal log within timeout window (should use same session)
            response2 = self.client.post(
                '/api/v1/meal_logs/',
                headers=self.auth_headers,
                data={
                    'timestamp': '2024-01-15T12:05:00Z',
                    'total_weight_g': '200.0',
                    'delta_weight_g': '100.0',
                },
                files={'image_file': ('test2.jpg', image_data, 'image/jpeg')}
            )
        
        self.assertEqual(response1.status_code, 201)
        self.assertEqual(response2.status_code, 201)
        
        data1 = response1.json()
        data2 = response2.json()
        
        # Should use the same meal session
        self.assertEqual(data1['meal_session_id'], data2['meal_session_id'])
        
        # Session totals should be cumulative
        self.assertEqual(float(data2['session_totals']['total_kcal']), 260.0)  # 130 * 2
