#!/usr/bin/env python3
"""
Django management script to create test device and user data.
Run this inside Docker container to set up test data.
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.local')
sys.path.append('/app')
django.setup()

from django.contrib.auth import get_user_model
from app.models import Device, DeviceSession, UserDevice

User = get_user_model()

def create_test_data():
    """Create test user, device, and session"""
    print("🔧 Creating test data...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print("✅ Created test user")
    else:
        print("✅ Test user already exists")
    
    # Create test device
    device, created = Device.objects.get_or_create(
        device_id='Dev001',
        defaults={
            'device_name': 'Test Smart Plate',
            'device_type': 'esp32',
            'firmware_version': '1.0.0'
        }
    )
    if created:
        print("✅ Created test device")
    else:
        print("✅ Test device already exists")
    
    # Create device session
    session, created = DeviceSession.objects.get_or_create(
        device=device,
        access_token='dummy',
        defaults={
            'expires_at': datetime.now() + timedelta(days=30)
        }
    )
    if created:
        print("✅ Created device session")
    else:
        print("✅ Device session already exists")
    
    # Link device to user
    user_device, created = UserDevice.objects.get_or_create(
        device=device,
        user=user,
        defaults={
            'linked_at': datetime.now()
        }
    )
    if created:
        print("✅ Linked device to user")
    else:
        print("✅ Device already linked to user")
    
    print(f"\n🎉 Test data ready!")
    print(f"   User: {user.username}")
    print(f"   Device ID: {device.device_id}")
    print(f"   Access Token: {session.access_token}")
    print(f"   Session expires: {session.expires_at}")

if __name__ == "__main__":
    create_test_data()
