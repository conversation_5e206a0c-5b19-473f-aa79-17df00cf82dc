#!/usr/bin/env python3
"""
Simple test script to verify the async fix works.
This creates test data and makes a meal log API call.
"""

import requests
import json
import io
from PIL import Image
from datetime import datetime

def create_test_image():
    """Create a simple test image"""
    img = Image.new('RGB', (640, 480), color=(139, 69, 19))  # Brown color for food
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.ellipse([100, 100, 300, 250], fill=(255, 255, 255))  # White circle (idli)

    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_meal_log_api():
    """Test the meal logging API"""
    base_url = "http://localhost:8000"

    # Test with dummy device credentials
    headers = {
        "device-id": "Dev001",
        "authorization": "Bearer dummy"
    }

    data = {
        "timestamp": datetime.now().isoformat() + "Z",
        "total_weight_g": "140.0",
        "delta_weight_g": "140.0"
    }

    test_image = create_test_image()
    files = {'image_file': ('test_food.jpg', test_image, 'image/jpeg')}

    print("🧪 Testing meal log API with async fix...")
    print(f"URL: {base_url}/api/v1/meal_logs/")
    print(f"Headers: {headers}")
    print(f"Data: {data}")

    try:
        response = requests.post(
            f"{base_url}/api/v1/meal_logs/",
            headers=headers,
            data=data,
            files=files,
            timeout=30
        )

        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")

        if response.status_code == 422:
            print("❌ Validation Error (Expected - need real device setup)")
            print(f"Response: {response.text}")
        elif response.status_code == 500:
            print("❌ Internal Server Error (This should be fixed now)")
            print(f"Response: {response.text}")
            # Let's also check if it's still the async error
            if "async context" in response.text:
                print("🔧 Still getting async context error - needs more fixes")
                print("🔧 The new simplified approach should fix this!")
            else:
                print("🔧 Different error - async might be fixed")
        elif response.status_code == 401:
            print("✅ Authentication Error (Expected - dummy credentials)")
            print("🎉 This means async fix worked! No more 500 errors.")
            print(f"Response: {response.text}")
        elif response.status_code == 201:
            print("✅ Success! Meal log created")
            result = response.json()
            print(f"Meal Log ID: {result.get('meal_log_id')}")
            print(f"AI Suggestions: {result.get('ai_suggestions', [])}")
            print(f"Food Entries: {len(result.get('entries', []))}")
        else:
            print(f"📊 Other response: {response.status_code}")
            print(f"Response: {response.text}")

            # If it's a 500 error, let's check Docker logs
            if response.status_code == 500:
                print("\n🔍 To debug this error, check Docker logs:")
                print("docker-compose -f docker-compose.dev.yml logs web | tail -20")

    except requests.exceptions.ConnectionError:
        print("❌ Connection Error - Make sure Docker containers are running")
        print("Run: docker-compose -f docker-compose.dev.yml up")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_meal_log_api()
