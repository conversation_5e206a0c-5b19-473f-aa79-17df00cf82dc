"""
Meal logs schemas for the API.
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class AISuggestion(BaseModel):
    """Schema for AI food classification suggestions."""
    
    label: str = Field(..., description="Suggested food name")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score between 0 and 1")


class FoodEntryResponse(BaseModel):
    """Schema for food entry in API responses."""
    
    id: int = Field(..., description="Food entry ID")
    label: str = Field(..., description="Food name")
    weight_g: Decimal = Field(..., description="Weight in grams")
    estimated_kcal: Decimal = Field(..., description="Estimated calories")
    protein_g: Decimal = Field(..., description="Protein content in grams")
    carbs_g: Decimal = Field(..., description="Carbohydrate content in grams")
    fat_g: Decimal = Field(..., description="Fat content in grams")
    fibre_g: Decimal = Field(..., description="Fiber content in grams")
    source: str = Field(..., description="Source of the label (ai_auto, user_plate, user_app)")
    manual_override: bool = Field(..., description="Whether manually corrected")


class SessionTotals(BaseModel):
    """Schema for meal session nutritional totals."""
    
    total_kcal: Decimal = Field(..., description="Total calories in session")
    protein_g: Decimal = Field(..., description="Total protein in grams")
    carbs_g: Decimal = Field(..., description="Total carbohydrates in grams")
    fat_g: Decimal = Field(..., description="Total fat in grams")
    fibre_g: Decimal = Field(..., description="Total fiber in grams")


class MealLogResponse(BaseModel):
    """Schema for meal log API response."""
    
    meal_log_id: str = Field(..., description="Meal log UUID")
    meal_session_id: str = Field(..., description="Meal session UUID")
    status: str = Field(..., description="Classification status (confirmed, pending_label, rejected)")
    image_url: str = Field(..., description="URL of the uploaded image")
    ai_suggestions: List[AISuggestion] = Field(default_factory=list, description="AI classification suggestions")
    entries: List[FoodEntryResponse] = Field(default_factory=list, description="Food entries detected in this log")
    session_totals: SessionTotals = Field(..., description="Current session nutritional totals")


class MealLogRequest(BaseModel):
    """Schema for meal log creation request (form data)."""
    
    timestamp: datetime = Field(..., description="Capture timestamp in ISO 8601 format")
    total_weight_g: Decimal = Field(..., gt=0, description="Total weight on plate in grams")
    delta_weight_g: Decimal = Field(..., description="Weight change from previous measurement")
    device_id: str = Field(..., min_length=1, max_length=64, description="Device identifier")
    # image_file is handled separately as UploadFile in the endpoint


class DeviceAuthRequest(BaseModel):
    """Schema for device authentication in headers."""
    
    device_id: str = Field(..., description="Device identifier")
    access_token: str = Field(..., description="Device session access token")


class ErrorResponse(BaseModel):
    """Schema for API error responses."""
    
    detail: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Specific error code")
