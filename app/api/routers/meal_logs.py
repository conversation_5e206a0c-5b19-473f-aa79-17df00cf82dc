"""
Meal logs endpoints for the API.

Handles meal logging from ESP32 devices including image upload,
AI classification, and meal session management.
"""

import logging
from datetime import datetime
from decimal import Decimal

from django.db import transaction
from asgiref.sync import sync_to_async
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status

from app.api.dependencies.device_auth import DeviceAuthInfo, get_device_with_user
from app.api.schemas.meal_logs import (
    AISuggestion,
    ErrorResponse,
    FoodEntryResponse,
    MealLogResponse,
    SessionTotals,
)
from app.models import FoodEntry, MealLog
from app.models.food_entry import FoodEntrySource
from app.models.meal_log import MealLogStatus
from app.services.ai_classification import ai_service
from app.services.meal_session import meal_session_service

logger = logging.getLogger(__name__)

router = APIRouter()


def get_latest_meal_log_sync(device, user):
    """Get latest meal log (sync function)."""
    try:
        return meal_session_service.get_latest_meal_log(device, user)
    except Exception as e:
        logger.error(f"Error getting latest meal log: {e}")
        return None


def create_meal_session_and_log_sync(device, user, timestamp, image_file, total_weight_g, delta_weight_g, status_value, ai_suggestions, classification_results):
    """Create meal session, log, and food entries in a transaction (sync function)."""
    try:
        with transaction.atomic():
            # Find or create meal session
            meal_session = meal_session_service.find_or_create_session(
                device=device,
                user=user,
                timestamp=timestamp
            )

            # Create meal log
            meal_log = MealLog.objects.create(
                meal_session=meal_session,
                timestamp=timestamp,
                image=image_file,
                total_weight_g=total_weight_g,
                delta_weight_g=delta_weight_g,
                status=status_value,
                ai_suggestions=[suggestion.dict() for suggestion in ai_suggestions],
            )

            # Create food entries
            food_entries = []
            for result in classification_results:
                if result.confidence >= 0.4:  # Only create entries for reasonable confidence
                    food_entry = FoodEntry.objects.create(
                        meal_log=meal_log,
                        label=result.label,
                        weight_g=result.weight_g,
                        estimated_kcal=result.estimated_kcal,
                        protein_g=result.protein_g,
                        carbs_g=result.carbs_g,
                        fat_g=result.fat_g,
                        fibre_g=result.fibre_g,
                        source=FoodEntrySource.AI_AUTO,
                        manual_override=False,
                    )
                    food_entries.append(food_entry)

            # Update session totals
            meal_session_service.update_session_totals(meal_session)

            # Get updated session totals
            session_totals = meal_session_service.get_session_totals(meal_session)

            return meal_log, food_entries, session_totals, meal_session
    except Exception as e:
        logger.error(f"Error creating meal session and log: {e}")
        raise


# Async wrappers
@sync_to_async
def _get_latest_meal_log(device, user):
    """Async wrapper for get_latest_meal_log_sync."""
    return get_latest_meal_log_sync(device, user)


@sync_to_async
def _create_meal_session_and_log(device, user, timestamp, image_file, total_weight_g, delta_weight_g, status_value, ai_suggestions, classification_results):
    """Async wrapper for create_meal_session_and_log_sync."""
    return create_meal_session_and_log_sync(device, user, timestamp, image_file, total_weight_g, delta_weight_g, status_value, ai_suggestions, classification_results)


@router.post(
    "/",
    response_model=MealLogResponse,
    status_code=status.HTTP_201_CREATED,
    responses={
        400: {"model": ErrorResponse, "description": "Bad request"},
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Device not linked to user"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
)
async def create_meal_log(
    timestamp: datetime = Form(..., description="Capture timestamp in ISO 8601 format"),
    total_weight_g: Decimal = Form(..., gt=0, description="Total weight on plate in grams"),
    delta_weight_g: Decimal = Form(..., description="Weight change from previous measurement"),
    image_file: UploadFile = File(..., description="Food image (JPEG/PNG)"),
    device_auth: DeviceAuthInfo = Depends(get_device_with_user),
) -> MealLogResponse:
    """
    Create a new meal log from ESP32 device.
    
    This endpoint:
    1. Validates and stores the uploaded image
    2. Runs AI food classification
    3. Creates/updates meal session
    4. Creates meal log and food entries
    5. Updates session nutritional totals
    6. Returns classification results and session totals
    
    **Authentication**: Requires device authentication via headers:
    - `device-id`: Device identifier
    - `authorization`: Bearer token with device access token
    
    **Form Data**:
    - `timestamp`: ISO 8601 timestamp of when the image was captured
    - `total_weight_g`: Total weight on the plate after food was added
    - `delta_weight_g`: Weight difference from the previous measurement
    - `image_file`: Food image file (JPEG or PNG format)
    
    **Response**: Contains AI classification results, food entries, and session totals
    """
    try:
        # Validate image file
        if not image_file.content_type or not image_file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid image file. Only JPEG and PNG formats are supported."
            )
        
        # Validate weight values
        if delta_weight_g < 0 and abs(delta_weight_g) > total_weight_g:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid weight values. Delta weight cannot exceed total weight."
            )
        
        logger.info(f"Processing meal log for device {device_auth.device.device_id}, user {device_auth.user.id}")
        
        # Get previous meal log for differential analysis
        previous_log = await _get_latest_meal_log(device_auth.device, device_auth.user)
        previous_image_url = previous_log.image.url if previous_log and previous_log.image else None
        
        # Run AI classification
        logger.info("Running AI food classification")
        classification_results = await ai_service.classify_food(
            image_file=image_file,
            delta_weight_g=delta_weight_g,
            previous_image_url=previous_image_url
        )
        
        # Determine status based on classification results
        if not classification_results:
            status_value = MealLogStatus.PENDING_LABEL
            ai_suggestions = []
        elif len(classification_results) == 1 and classification_results[0].confidence >= 0.7:
            status_value = MealLogStatus.CONFIRMED
            ai_suggestions = [
                AISuggestion(label=result.label, confidence=result.confidence)
                for result in classification_results
            ]
        else:
            # Multiple results or medium confidence
            status_value = MealLogStatus.PENDING_LABEL
            ai_suggestions = [
                AISuggestion(label=result.label, confidence=result.confidence)
                for result in classification_results
            ]
        
        # Create database records in a transaction (async)
        meal_log, food_entries, session_totals, meal_session = await _create_meal_session_and_log(
            device=device_auth.device,
            user=device_auth.user,
            timestamp=timestamp,
            image_file=image_file,
            total_weight_g=total_weight_g,
            delta_weight_g=delta_weight_g,
            status_value=status_value,
            ai_suggestions=ai_suggestions,
            classification_results=classification_results
        )
        
        # Prepare response
        food_entry_responses = [
            FoodEntryResponse(
                id=entry.id,
                label=entry.label,
                weight_g=entry.weight_g,
                estimated_kcal=entry.estimated_kcal,
                protein_g=entry.protein_g,
                carbs_g=entry.carbs_g,
                fat_g=entry.fat_g,
                fibre_g=entry.fibre_g,
                source=entry.source,
                manual_override=entry.manual_override,
            )
            for entry in food_entries
        ]
        
        session_totals_response = SessionTotals(**session_totals)
        
        response = MealLogResponse(
            meal_log_id=str(meal_log.id),
            meal_session_id=str(meal_session.id),
            status=meal_log.status,
            image_url=meal_log.image.url,
            ai_suggestions=ai_suggestions,
            entries=food_entry_responses,
            session_totals=session_totals_response,
        )
        
        logger.info(f"Successfully created meal log {meal_log.id} with {len(food_entries)} food entries")
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    
    except Exception as e:
        logger.error(f"Error creating meal log: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while processing meal log"
        )
