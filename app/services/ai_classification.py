"""
AI Food Classification Service

Handles food classification using AI models with support for both
mock responses (development) and real OpenAI integration (production).
"""

import logging
import os
import random
from decimal import Decimal
from typing import List, Optional

from django.core.files.uploadedfile import UploadedFile

logger = logging.getLogger(__name__)


class FoodClassificationResult:
    """Result of food classification with nutritional data."""
    
    def __init__(
        self,
        label: str,
        confidence: float,
        weight_g: Decimal,
        estimated_kcal: Decimal,
        protein_g: Decimal = Decimal('0'),
        carbs_g: Decimal = Decimal('0'),
        fat_g: Decimal = Decimal('0'),
        fibre_g: Decimal = Decimal('0'),
    ):
        self.label = label
        self.confidence = confidence
        self.weight_g = weight_g
        self.estimated_kcal = estimated_kcal
        self.protein_g = protein_g
        self.carbs_g = carbs_g
        self.fat_g = fat_g
        self.fibre_g = fibre_g


class AIClassificationService:
    """Service for AI-powered food classification."""
    
    # Mock food database for development
    MOCK_FOODS = {
        'idli': {
            'kcal_per_100g': 58,
            'protein_per_100g': 2.0,
            'carbs_per_100g': 12.0,
            'fat_per_100g': 0.3,
            'fibre_per_100g': 0.8,
        },
        'sambar': {
            'kcal_per_100g': 85,
            'protein_per_100g': 3.5,
            'carbs_per_100g': 12.0,
            'fat_per_100g': 3.0,
            'fibre_per_100g': 2.5,
        },
        'dosa': {
            'kcal_per_100g': 168,
            'protein_per_100g': 4.0,
            'carbs_per_100g': 28.0,
            'fat_per_100g': 4.0,
            'fibre_per_100g': 1.2,
        },
        'rice': {
            'kcal_per_100g': 130,
            'protein_per_100g': 2.7,
            'carbs_per_100g': 28.0,
            'fat_per_100g': 0.3,
            'fibre_per_100g': 0.4,
        },
        'chapati': {
            'kcal_per_100g': 297,
            'protein_per_100g': 11.0,
            'carbs_per_100g': 58.0,
            'fat_per_100g': 4.0,
            'fibre_per_100g': 3.0,
        },
        'dal': {
            'kcal_per_100g': 116,
            'protein_per_100g': 9.0,
            'carbs_per_100g': 20.0,
            'fat_per_100g': 0.4,
            'fibre_per_100g': 8.0,
        },
    }
    
    def __init__(self, use_mock: bool = None):
        """Initialize the AI classification service.
        
        Args:
            use_mock: If True, use mock responses. If None, auto-detect based on environment.
        """
        if use_mock is None:
            # Auto-detect: use mock if no OpenAI API key is set
            use_mock = not bool(os.getenv('OPENAI_API_KEY'))
        
        self.use_mock = use_mock
        self.openai_client = None
        
        if not use_mock:
            try:
                import openai
                self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
                logger.info("OpenAI client initialized for production AI classification")
            except ImportError:
                logger.warning("OpenAI package not installed, falling back to mock responses")
                self.use_mock = True
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}, falling back to mock responses")
                self.use_mock = True
        
        if self.use_mock:
            logger.info("Using mock AI classification responses for development")
    
    async def classify_food(
        self,
        image_file: UploadedFile,
        delta_weight_g: Decimal,
        previous_image_url: Optional[str] = None
    ) -> List[FoodClassificationResult]:
        """Classify food in the image and return nutritional estimates.
        
        Args:
            image_file: Uploaded image file
            delta_weight_g: Weight change since previous measurement
            previous_image_url: URL of previous image for differential analysis
            
        Returns:
            List of food classification results with confidence scores
        """
        if self.use_mock:
            return await self._mock_classify_food(delta_weight_g)
        else:
            return await self._openai_classify_food(image_file, delta_weight_g, previous_image_url)
    
    async def _mock_classify_food(self, delta_weight_g: Decimal) -> List[FoodClassificationResult]:
        """Generate mock food classification results for development."""
        # Simulate different confidence scenarios
        confidence_scenario = random.choice(['high', 'medium', 'low', 'ambiguous'])
        
        if confidence_scenario == 'high':
            # High confidence single prediction
            food_name = random.choice(list(self.MOCK_FOODS.keys()))
            return [self._create_food_result(food_name, 0.85, delta_weight_g)]
        
        elif confidence_scenario == 'medium':
            # Medium confidence single prediction
            food_name = random.choice(list(self.MOCK_FOODS.keys()))
            return [self._create_food_result(food_name, 0.65, delta_weight_g)]
        
        elif confidence_scenario == 'low':
            # Low confidence - return empty list to trigger pending_label
            return []
        
        else:  # ambiguous
            # Multiple similar confidence predictions
            foods = random.sample(list(self.MOCK_FOODS.keys()), 2)
            return [
                self._create_food_result(foods[0], 0.55, delta_weight_g),
                self._create_food_result(foods[1], 0.52, delta_weight_g),
            ]
    
    async def _openai_classify_food(
        self,
        image_file: UploadedFile,
        delta_weight_g: Decimal,
        previous_image_url: Optional[str] = None
    ) -> List[FoodClassificationResult]:
        """Use OpenAI Vision API for food classification."""
        try:
            # Convert image to base64 for OpenAI API
            import base64
            image_data = base64.b64encode(image_file.read()).decode('utf-8')
            image_file.seek(0)  # Reset file pointer
            
            # Prepare the prompt
            prompt = self._build_openai_prompt(delta_weight_g, previous_image_url)
            
            # Call OpenAI Vision API
            response = self.openai_client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500
            )
            
            # Parse OpenAI response and convert to FoodClassificationResult
            return self._parse_openai_response(response, delta_weight_g)
            
        except Exception as e:
            logger.error(f"OpenAI classification failed: {e}")
            # Fallback to mock response
            return await self._mock_classify_food(delta_weight_g)
    
    def _create_food_result(
        self,
        food_name: str,
        confidence: float,
        weight_g: Decimal
    ) -> FoodClassificationResult:
        """Create a FoodClassificationResult with nutritional calculations."""
        food_data = self.MOCK_FOODS.get(food_name, self.MOCK_FOODS['rice'])  # Default to rice
        
        # Calculate nutritional values based on weight
        weight_factor = weight_g / Decimal('100')  # Convert to per-100g factor
        
        return FoodClassificationResult(
            label=food_name.title(),
            confidence=confidence,
            weight_g=weight_g,
            estimated_kcal=Decimal(str(food_data['kcal_per_100g'])) * weight_factor,
            protein_g=Decimal(str(food_data['protein_per_100g'])) * weight_factor,
            carbs_g=Decimal(str(food_data['carbs_per_100g'])) * weight_factor,
            fat_g=Decimal(str(food_data['fat_per_100g'])) * weight_factor,
            fibre_g=Decimal(str(food_data['fibre_per_100g'])) * weight_factor,
        )
    
    def _build_openai_prompt(self, delta_weight_g: Decimal, previous_image_url: Optional[str]) -> str:
        """Build the prompt for OpenAI Vision API."""
        base_prompt = f"""
        Analyze this food image and identify the food items. The weight added is {delta_weight_g}g.
        
        Please respond with a JSON array of food items, each containing:
        - label: food name
        - confidence: confidence score (0.0 to 1.0)
        
        Focus on Indian cuisine. If confidence is below 0.4, return an empty array.
        If multiple items have similar confidence (within 0.1), include all of them.
        
        Example response:
        [{"label": "Idli", "confidence": 0.85}]
        """
        
        if previous_image_url:
            base_prompt += f"\n\nThis is a differential analysis. Compare with the previous state to identify only the newly added food items."
        
        return base_prompt
    
    def _parse_openai_response(
        self,
        response,
        delta_weight_g: Decimal
    ) -> List[FoodClassificationResult]:
        """Parse OpenAI response and convert to FoodClassificationResult objects."""
        try:
            import json
            content = response.choices[0].message.content
            
            # Extract JSON from response
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            if start_idx == -1 or end_idx == 0:
                return []
            
            json_str = content[start_idx:end_idx]
            predictions = json.loads(json_str)
            
            results = []
            for pred in predictions:
                if pred.get('confidence', 0) >= 0.4:  # Minimum confidence threshold
                    food_name = pred['label'].lower()
                    results.append(
                        self._create_food_result(food_name, pred['confidence'], delta_weight_g)
                    )
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to parse OpenAI response: {e}")
            return []


# Global service instance
ai_service = AIClassificationService()
